﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Diretor.D_TelaPrincipal
{
    public partial class D_TelaPrincipal : Form
    {
        public D_TelaPrincipal()
        {
            InitializeComponent();
        }

        private void AbrirTela(Form novaTela)
        {
            bool estaMaximizado = this.WindowState == FormWindowState.Maximized;

            this.Hide();
            if (estaMaximizado)
                novaTela.WindowState = FormWindowState.Maximized;

            novaTela.FormClosed += (s, args) =>
            {
                this.Show();
                if (estaMaximizado)
                    this.WindowState = FormWindowState.Maximized;
            };

            novaTela.Show();
        }



        private void btn_minimizar_telaprincipal_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_telaprincipal_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_coordenadores_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Diretor.D_Coordenador());
        }

        private void btn_fechar_telaprincipal_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
        "Deseja realmente fechar a aplicação?",
        "Confirmação",
        MessageBoxButtons.YesNo,
        MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_conferirgrade_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Coordenador.C_ConferirGrade2.C_ConferirGrade2());
        }

        private void btn_curso_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Diretor.D_Curso());
        }

        private void btn_visualizarcoordenadores_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Diretor.D_VisualizarCoordenadores.D_VisualizarCoordenadores());
        }

        private void btn_visualizarcurso_telaprincipal_Click(object sender, EventArgs e)
        {
            // TODO: Implementar D_VisualizarCursos - arquivo não existe ainda
            MessageBox.Show("Funcionalidade de visualizar cursos ainda não implementada.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Information);
            // AbrirTela(new ProjetoIntegrador.Diretor.D_VisualizarCursos.D_VisualizarCursos());
        }
    }
}

using System;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using System.Security.Cryptography;

namespace ProjetoIntegrador.Login
{
    public partial class Login : Form
    {
        private bool senhaVisivel = false;

        public Login()
        {
            InitializeComponent();
            // Configurar senha oculta desde o início
            ConfigurarSenhaOculta();
        }

        /// <summary>
        /// Configura o estado inicial da senha como oculta
        /// </summary>
        private void ConfigurarSenhaOculta()
        {
            senhaVisivel = false;
            txt_senha_login.UseSystemPasswordChar = true;
            // Opcional: definir texto do botão se necessário
            // btn_visualizarsenha_login.Text = "👁"; // ou ícone desejado
        }

        private string GerarMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                    sb.Append(b.ToString("x2"));
                return sb.ToString();
            }
        }

        private void btn_entrar_Click_1(object sender, EventArgs e)
        {
            string ra = txt_RA_login.Text.Trim();
            if (string.IsNullOrEmpty(ra) || string.IsNullOrEmpty(txt_senha_login.Text.Trim()))
            {
                MessageBox.Show("Por favor, preencha o RA e a Senha.");
                return;
            }

            string senhaMD5 = GerarMD5(txt_senha_login.Text.Trim());

            try
            {
                string conexaoString = "SERVER=localhost; UID=root; PASSWORD=; DATABASE=sehd";
                using (MySqlConnection conexao = new MySqlConnection(conexaoString))
                {
                    conexao.Open();

                    string query = @"
                        SELECT x.tipo 
                        FROM (
                            SELECT d.RA, d.data_nasc_diretor AS senha, 'D' AS tipo FROM diretor d
                            UNION ALL
                            SELECT c.RA, c.data_nasc_coord AS senha, 'C' AS tipo FROM coordenador c
                            UNION ALL
                            SELECT p.RA, p.data_nasc_prof AS senha, 'P' AS tipo FROM professor p
                        ) x
                        WHERE x.RA = @RA AND x.senha = @SENHA";

                    using (MySqlCommand comando = new MySqlCommand(query, conexao))
                    {
                        comando.Parameters.AddWithValue("@RA", ra);
                        comando.Parameters.AddWithValue("@SENHA", senhaMD5);

                        MySqlDataAdapter adaptador = new MySqlDataAdapter(comando);
                        DataTable resultado = new DataTable();
                        adaptador.Fill(resultado);

                        if (resultado.Rows.Count > 0)
                        {
                            string tipo = resultado.Rows[0]["tipo"].ToString();
                            MessageBox.Show(tipo switch
                            {
                                "C" => "Seja bem-vindo(a) Coordenador(a)!",
                                "P" => "Seja bem-vindo(a) Professor(a)!",
                                "D" => "Seja bem-vindo(a) Diretor(a)!",
                                _ => "Seja bem-vindo(a)!"
                            });

                            Form tela = tipo switch
                            {
                                "P" => new ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal(),
                                "C" => new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal(),
                                "D" => new ProjetoIntegrador.Diretor.D_TelaPrincipal.D_TelaPrincipal(),
                                _ => null
                            };

                            tela?.Show();
                            this.Hide();
                        }
                        else
                        {
                            MessageBox.Show("RA ou senha incorretos.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao conectar com o banco: " + ex.Message);
            }
        }

        /// <summary>
        /// Alterna entre mostrar e ocultar a senha
        /// </summary>
        private void btn_visualizarsenha_login_Click(object sender, EventArgs e)
        {
            // Alterna o estado da visibilidade
            senhaVisivel = !senhaVisivel;

            // Aplica a configuração de visibilidade
            txt_senha_login.UseSystemPasswordChar = !senhaVisivel;

            // Opcional: Atualizar texto do botão (se não estiver usando apenas ícone)
            // btn_visualizarsenha_login.Text = senhaVisivel ? "🙈" : "👁";

            // Opcional: Atualizar tooltip para melhor UX
            // ToolTip tooltip = new ToolTip();
            // tooltip.SetToolTip(btn_visualizarsenha_login, senhaVisivel ? "Ocultar senha" : "Mostrar senha");
        }

        /// <summary>
        /// Configurações iniciais do formulário de login
        /// </summary>
        private void Login_Load(object sender, EventArgs e)
        {
            // Garantir que a senha inicie oculta
            ConfigurarSenhaOculta();
        }

        private void btn_minimizar_login_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_login_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_fechar_coordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar a aplicação?",
             "Confirmação",
             MessageBoxButtons.YesNo,
             MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }
    }
}

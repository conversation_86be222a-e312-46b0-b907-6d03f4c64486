using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Security.Cryptography;
using ProjetoIntegrador.Coordenador.C_TelaPrincipal;
using ProjetoIntegrador.Classes;
using ProjetoIntegrador.conn;

namespace ProjetoIntegrador.C_Professor
{
    public partial class C_Professor : Form
    {
        public C_Professor()
        {
            InitializeComponent();
            CarregarDadosComboBoxes();
        }

        // Método para gerar hash MD5
        private string GerarMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                    sb.Append(b.ToString("x2"));
                return sb.ToString();
            }
        }

        // Método para carregar dados nas ComboBoxes
        private void CarregarDadosComboBoxes()
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();

                // Carregar disciplinas
                DataTable disciplinas = cmdBanco.ConsultarDisciplinas();
                cb_nomedisciplina_professor.Items.Clear();
                cb_nomedisciplina_professor.Items.Add("Selecione uma disciplina");
                foreach (DataRow row in disciplinas.Rows)
                {
                    cb_nomedisciplina_professor.Items.Add(row["NOME_DISCIPLINA"].ToString());
                }
                cb_nomedisciplina_professor.SelectedIndex = 0;

                // Carregar cursos
                DataTable cursos = cmdBanco.ConsultarCursos();
                cb_nomecurso_professor.Items.Clear();
                cb_nomecurso_professor.Items.Add("Selecione um curso");
                foreach (DataRow row in cursos.Rows)
                {
                    cb_nomecurso_professor.Items.Add(row["NOME_CURSO"].ToString());
                }
                cb_nomecurso_professor.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dados: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lbl_senhatext_Click(object sender, EventArgs e)
        {

        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Validar se todos os campos estão preenchidos
            if (string.IsNullOrWhiteSpace(txt_nomeprofessor_professor.Text) ||
                string.IsNullOrWhiteSpace(txt_RA_professor.Text) ||
                cb_nomedisciplina_professor.SelectedIndex <= 0 ||
                cb_nomecurso_professor.SelectedIndex <= 0)
            {
                MessageBox.Show("Todos os campos precisam ser preenchidos.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Validar se o RA é um número válido
            if (!int.TryParse(txt_RA_professor.Text, out int ra))
            {
                MessageBox.Show("O RA deve ser um número válido.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Converter a data de nascimento para o formato DDMMAAAA
                DateTime dataNascimento = dtp_datadenascimentoprofessor_professor.Value;
                string dataFormatada = dataNascimento.ToString("ddMMyyyy");

                // Gerar hash MD5 da data de nascimento
                string dataNascimentoMD5 = GerarMD5(dataFormatada);

                // Criar o modelo com os dados do formulário
                variaveisbanco modelo = new variaveisbanco
                {
                    nomeprofessor = txt_nomeprofessor_professor.Text.Trim(),
                    raprofessor = ra,
                    datanascimentoprofessor = dataNascimentoMD5,
                    nomecurso = cb_nomecurso_professor.SelectedItem.ToString()
                };

                // Instanciar a classe de comandos do banco
                comandosbanco cmdBanco = new comandosbanco();

                // VALIDAÇÃO DE CONFLITO POR DISCIPLINA
                // Verificar se já existe outro professor lecionando a mesma disciplina
                string disciplinaSelecionada = cb_nomedisciplina_professor.Text;

                if (VerificarConflitoDisponibilidadePorDisciplina(disciplinaSelecionada))
                {
                    MessageBox.Show($"Não é possível cadastrar este professor.\n" +
                                  $"Já existe outro professor lecionando a disciplina '{disciplinaSelecionada}'.\n\n" +
                                  $"Apenas um professor pode lecionar por disciplina por noite.\n" +
                                  $"Por favor, selecione uma disciplina diferente.",
                                  "Conflito de Disciplina", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Cadastrar o professor (método original)
                cmdBanco.CadastroProfessor(modelo);

                MessageBox.Show("Professor cadastrado com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Limpar os campos após o cadastro
                txt_nomeprofessor_professor.Clear();
                txt_RA_professor.Clear();
                cb_nomedisciplina_professor.SelectedIndex = 0;
                cb_nomecurso_professor.SelectedIndex = 0;
                dtp_datadenascimentoprofessor_professor.Value = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao cadastrar professor: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_voltar_professor_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }

        private void btn_minimizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_professor_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_fechar_professor_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        /// <summary>
        /// Verifica se já existe outro professor lecionando a mesma disciplina
        /// </summary>
        /// <param name="nomeDisciplina">Nome da disciplina a ser verificada</param>
        /// <returns>True se há conflito, False se não há conflito</returns>
        private bool VerificarConflitoDisponibilidadePorDisciplina(string nomeDisciplina)
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();

                // Escape de aspas simples no nome da disciplina
                string disciplinaEscapada = nomeDisciplina.Replace("'", "''");

                // Query para verificar se já existe professor lecionando especificamente esta disciplina
                // A relação é feita através do curso: professor -> curso -> disciplinas do curso
                // Mas como não há vínculo direto professor-disciplina, vamos verificar se já existe
                // professor no mesmo curso da disciplina selecionada

                string query = @"SELECT COUNT(*) as TOTAL
                               FROM PROFESSOR P
                               INNER JOIN CURSO C ON P.ID_CURSO = C.ID_CURSO
                               INNER JOIN DISCIPLINA D ON C.ID_CURSO = D.ID_CURSO
                               WHERE D.NOME_DISCIPLINA = '" + disciplinaEscapada + @"'
                               AND P.ID_DISPONIBILIDADE IS NOT NULL";

                var resultado = cmdBanco.SQL.CarregaDados(query);

                int total = Convert.ToInt32(resultado.Rows[0]["TOTAL"]);

                // Retorna true se já existe professor com disponibilidade no curso desta disciplina
                return total > 0;
            }
            catch (Exception ex)
            {
                // Em caso de erro, registrar e permitir o cadastro (comportamento conservador)
                System.Diagnostics.Debug.WriteLine($"Erro na validação de conflito por disciplina: {ex.Message}");
                return false;
            }
        }
    }
}

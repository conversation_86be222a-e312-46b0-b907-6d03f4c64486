﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Professor
{
    public partial class P_IDisponibilidade : Form
    {

        public P_IDisponibilidade()
        {
            InitializeComponent();
            CarregarDiasSemana();
        }

        // Método para carregar os dias da semana no ComboBox
        private void CarregarDiasSemana()
        {
            try
            {
                // Limpa o ComboBox
                ComboBox cbDias = Controls.Find("cb_selecionardias", true).FirstOrDefault() as ComboBox;

                if (cbDias != null)
                {
                    cbDias.Items.Clear();

                    // Adiciona um item padrão
                    cbDias.Items.Add("Selecione um dia...");

                    // Adiciona os dias da semana
                    cbDias.Items.Add("Segunda-feira");
                    cbDias.Items.Add("Terça-feira");
                    cbDias.Items.Add("Quarta-feira");
                    cbDias.Items.Add("Quinta-feira");
                    cbDias.Items.Add("Sexta-feira");
                    cbDias.Items.Add("Sábado");

                    // Seleciona o item padrão
                    cbDias.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dias da semana: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_fechar_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Validar se todos os campos estão preenchidos
            if (!ValidarCampos())
            {
                return;
            }

            try
            {
                // Capturar os valores dos campos
                string diaSemana = cb_selecionardias_disponibilidade.Text;
                string horarioInicio = txt_horarioinicio_disponibilidade.Text;
                string horarioFim = txt_horariofim_disponibilidade.Text;

                // Validar formato dos horários
                if (!TimeSpan.TryParse(horarioInicio, out TimeSpan inicio) ||
                    !TimeSpan.TryParse(horarioFim, out TimeSpan fim))
                {
                    MessageBox.Show("Por favor, insira os horários no formato correto (HH:MM).\n" +
                                   "Exemplo: 08:00 ou 14:30",
                                   "Formato Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Validar se horário de fim é maior que horário de início
                if (fim <= inicio)
                {
                    MessageBox.Show("O horário de fim deve ser maior que o horário de início.",
                                   "Horário Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Preencher instância da classe VariaveisBanco
                variaveisbanco dados = new variaveisbanco
                {
                    diasemana = diaSemana,
                    horarioinicio = inicio,
                    horariofim = fim
                };

                // Chamar o método InserirDisponibilidade
                comandosbanco cmdBanco = new comandosbanco();
                bool sucesso = cmdBanco.InserirDisponibilidade(dados);

                if (sucesso)
                {
                    // Exibir MessageBox de sucesso
                    MessageBox.Show($"Disponibilidade cadastrada com sucesso!\n\n" +
                                   $"📅 Dia: {dados.diasemana}\n" +
                                   $"🕐 Início: {dados.horarioinicio:hh\\:mm}\n" +
                                   $"🕐 Fim: {dados.horariofim:hh\\:mm}",
                                   "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpar campos após cadastro bem-sucedido
                    LimparCampos();
                }
                else
                {
                    // Exibir MessageBox de erro
                    MessageBox.Show("Erro ao cadastrar disponibilidade.\n" +
                                   "Verifique os dados e tente novamente.",
                                   "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (MySqlException sqlEx)
            {
                string mensagemErro = "Erro no banco de dados:\n";
                if (sqlEx.Message.Contains("Duplicate entry"))
                    mensagemErro += "Já existe uma disponibilidade igual cadastrada.";
                else
                    mensagemErro += sqlEx.Message;

                MessageBox.Show(mensagemErro, "Erro de Banco de Dados", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro inesperado ao cadastrar disponibilidade:\n{ex.Message}",
                               "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Valida se todos os campos obrigatórios estão preenchidos
        /// </summary>
        /// <returns>True se todos os campos estão válidos, False caso contrário</returns>
        private bool ValidarCampos()
        {
            // Validar ComboBox de dias
            if (cb_selecionardias_disponibilidade.SelectedIndex == -1)
            {
                MessageBox.Show("Por favor, selecione um dia da semana.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cb_selecionardias_disponibilidade.Focus();
                return false;
            }

            // Validar horário de início
            if (string.IsNullOrWhiteSpace(txt_horarioinicio_disponibilidade.Text))
            {
                MessageBox.Show("Por favor, informe o horário de início.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_horarioinicio_disponibilidade.Focus();
                return false;
            }

            // Validar horário de fim
            if (string.IsNullOrWhiteSpace(txt_horariofim_disponibilidade.Text))
            {
                MessageBox.Show("Por favor, informe o horário de fim.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_horariofim_disponibilidade.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Limpa todos os campos do formulário
        /// </summary>
        private void LimparCampos()
        {
            txt_horarioinicio_disponibilidade.Text = "";
            txt_horariofim_disponibilidade.Text = "";
            cb_selecionardias_disponibilidade.SelectedIndex = -1;

            // Focar no primeiro campo para facilitar nova entrada
            cb_selecionardias_disponibilidade.Focus();
        }

        private void P_IDisponibilidade_Load(object sender, EventArgs e)
        {
            // Preencher o ComboBox com os dias da semana
            PreencherComboBoxDias();
        }

        /// <summary>
        /// Preenche o ComboBox com os dias da semana
        /// </summary>
        private void PreencherComboBoxDias()
        {
            try
            {
                // Limpar o ComboBox antes de preencher
                cb_selecionardias_disponibilidade.Items.Clear();

                // Adicionar os dias da semana conforme solicitado
                cb_selecionardias_disponibilidade.Items.Add("Segunda-feira");
                cb_selecionardias_disponibilidade.Items.Add("Terça-feira");
                cb_selecionardias_disponibilidade.Items.Add("Quarta-feira");
                cb_selecionardias_disponibilidade.Items.Add("Quinta-feira");
                cb_selecionardias_disponibilidade.Items.Add("Sexta-feira");

                // Configurar propriedades do ComboBox
                cb_selecionardias_disponibilidade.DropDownStyle = ComboBoxStyle.DropDownList; // Impede edição manual
                cb_selecionardias_disponibilidade.SelectedIndex = -1; // Nenhum item selecionado inicialmente
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao carregar dias da semana: {ex.Message}",
                               "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}

﻿namespace ProjetoIntegrador.Coordenador.C_Disponibilidade
{
    partial class C_Disponibilidade
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(C_Disponibilidade));
            pnl_disponibilidade = new TableLayoutPanel();
            lbl_horario = new Label();
            lbl_dias = new Label();
            lbl_professor = new Label();
            pnl_header = new Panel();
            btn_voltar_disponibilidade = new Button();
            btn_minimizar_disponibilidade = new Button();
            btn_maximizar_disponibilidade = new Button();
            lbl_titulo = new Label();
            btn_fechar_disponibilidade = new Button();
            lbl_disponibilidade = new Label();
            btn_editar_disponibilidade_disponibilidade = new Button();
            btn_salvar_disponibilidade_disponibilidade = new Button();
            pnl_disponibilidade.SuspendLayout();
            pnl_header.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_disponibilidade
            // 
            pnl_disponibilidade.BackColor = Color.DarkGray;
            pnl_disponibilidade.CellBorderStyle = TableLayoutPanelCellBorderStyle.Inset;
            pnl_disponibilidade.ColumnCount = 3;
            pnl_disponibilidade.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            pnl_disponibilidade.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333359F));
            pnl_disponibilidade.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.3333359F));
            pnl_disponibilidade.Controls.Add(lbl_horario, 2, 0);
            pnl_disponibilidade.Controls.Add(lbl_dias, 1, 0);
            pnl_disponibilidade.Controls.Add(lbl_professor, 0, 0);
            pnl_disponibilidade.Location = new Point(236, 145);
            pnl_disponibilidade.Name = "pnl_disponibilidade";
            pnl_disponibilidade.RowCount = 10;
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Absolute, 25F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.RowStyles.Add(new RowStyle(SizeType.Percent, 11.1111107F));
            pnl_disponibilidade.Size = new Size(871, 483);
            pnl_disponibilidade.TabIndex = 18;
            // 
            // lbl_horario
            // 
            lbl_horario.AutoSize = true;
            lbl_horario.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_horario.ForeColor = Color.MidnightBlue;
            lbl_horario.Location = new Point(583, 2);
            lbl_horario.Name = "lbl_horario";
            lbl_horario.Padding = new Padding(100, 2, 0, 0);
            lbl_horario.Size = new Size(168, 23);
            lbl_horario.TabIndex = 21;
            lbl_horario.Text = "Horário";
            // 
            // lbl_dias
            // 
            lbl_dias.AutoSize = true;
            lbl_dias.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_dias.ForeColor = Color.MidnightBlue;
            lbl_dias.Location = new Point(294, 2);
            lbl_dias.Name = "lbl_dias";
            lbl_dias.Padding = new Padding(120, 2, 0, 0);
            lbl_dias.Size = new Size(163, 23);
            lbl_dias.TabIndex = 20;
            lbl_dias.Text = "Dias";
            // 
            // lbl_professor
            // 
            lbl_professor.AutoSize = true;
            lbl_professor.Font = new Font("Segoe UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_professor.ForeColor = Color.MidnightBlue;
            lbl_professor.Location = new Point(5, 2);
            lbl_professor.Name = "lbl_professor";
            lbl_professor.Padding = new Padding(97, 2, 0, 0);
            lbl_professor.Size = new Size(178, 23);
            lbl_professor.TabIndex = 0;
            lbl_professor.Text = "Professor";
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_disponibilidade);
            pnl_header.Controls.Add(btn_minimizar_disponibilidade);
            pnl_header.Controls.Add(btn_maximizar_disponibilidade);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_disponibilidade);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 21;
            pnl_header.Paint += pnl_header_Paint;
            // 
            // btn_voltar_disponibilidade
            // 
            btn_voltar_disponibilidade.BackColor = Color.MidnightBlue;
            btn_voltar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_voltar_disponibilidade.BackgroundImage");
            btn_voltar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_voltar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_voltar_disponibilidade.Location = new Point(35, 18);
            btn_voltar_disponibilidade.Name = "btn_voltar_disponibilidade";
            btn_voltar_disponibilidade.Size = new Size(30, 21);
            btn_voltar_disponibilidade.TabIndex = 23;
            btn_voltar_disponibilidade.UseVisualStyleBackColor = false;
            btn_voltar_disponibilidade.Click += btn_voltar_disponibilidade_Click;
            // 
            // btn_minimizar_disponibilidade
            // 
            btn_minimizar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_minimizar_disponibilidade.BackgroundImage");
            btn_minimizar_disponibilidade.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_minimizar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_minimizar_disponibilidade.Location = new Point(1173, 22);
            btn_minimizar_disponibilidade.Name = "btn_minimizar_disponibilidade";
            btn_minimizar_disponibilidade.Size = new Size(21, 19);
            btn_minimizar_disponibilidade.TabIndex = 22;
            btn_minimizar_disponibilidade.UseVisualStyleBackColor = true;
            btn_minimizar_disponibilidade.Click += btn_minimizar_disponibilidade_Click;
            // 
            // btn_maximizar_disponibilidade
            // 
            btn_maximizar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_maximizar_disponibilidade.BackgroundImage");
            btn_maximizar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_maximizar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_maximizar_disponibilidade.Location = new Point(1209, 18);
            btn_maximizar_disponibilidade.Name = "btn_maximizar_disponibilidade";
            btn_maximizar_disponibilidade.Size = new Size(19, 23);
            btn_maximizar_disponibilidade.TabIndex = 21;
            btn_maximizar_disponibilidade.UseVisualStyleBackColor = true;
            btn_maximizar_disponibilidade.Click += btn_maximizar_disponibilidade_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_disponibilidade
            // 
            btn_fechar_disponibilidade.BackColor = Color.MidnightBlue;
            btn_fechar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_fechar_disponibilidade.BackgroundImage");
            btn_fechar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_fechar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_fechar_disponibilidade.Location = new Point(1234, 18);
            btn_fechar_disponibilidade.Name = "btn_fechar_disponibilidade";
            btn_fechar_disponibilidade.Size = new Size(34, 21);
            btn_fechar_disponibilidade.TabIndex = 20;
            btn_fechar_disponibilidade.UseVisualStyleBackColor = false;
            btn_fechar_disponibilidade.Click += btn_fechar_disponibilidade_Click;
            // 
            // lbl_disponibilidade
            // 
            lbl_disponibilidade.AutoSize = true;
            lbl_disponibilidade.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_disponibilidade.ForeColor = Color.MidnightBlue;
            lbl_disponibilidade.Location = new Point(593, 99);
            lbl_disponibilidade.Name = "lbl_disponibilidade";
            lbl_disponibilidade.Size = new Size(151, 25);
            lbl_disponibilidade.TabIndex = 25;
            lbl_disponibilidade.Text = "Disponibilidade";
            // 
            // btn_editar_disponibilidade_disponibilidade
            // 
            btn_editar_disponibilidade_disponibilidade.BackColor = Color.Transparent;
            btn_editar_disponibilidade_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_editar_disponibilidade_disponibilidade.BackgroundImage");
            btn_editar_disponibilidade_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_editar_disponibilidade_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_editar_disponibilidade_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_editar_disponibilidade_disponibilidade.ForeColor = SystemColors.ControlLightLight;
            btn_editar_disponibilidade_disponibilidade.Location = new Point(982, 644);
            btn_editar_disponibilidade_disponibilidade.Name = "btn_editar_disponibilidade_disponibilidade";
            btn_editar_disponibilidade_disponibilidade.Size = new Size(125, 33);
            btn_editar_disponibilidade_disponibilidade.TabIndex = 26;
            btn_editar_disponibilidade_disponibilidade.Text = "Editar";
            btn_editar_disponibilidade_disponibilidade.UseVisualStyleBackColor = false;
            btn_editar_disponibilidade_disponibilidade.Click += btn_editar_disponibilidade_disponibilidade_Click;
            // 
            // btn_salvar_disponibilidade_disponibilidade
            // 
            btn_salvar_disponibilidade_disponibilidade.BackColor = Color.Transparent;
            btn_salvar_disponibilidade_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_salvar_disponibilidade_disponibilidade.BackgroundImage");
            btn_salvar_disponibilidade_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_salvar_disponibilidade_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_salvar_disponibilidade_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_salvar_disponibilidade_disponibilidade.ForeColor = SystemColors.ControlLightLight;
            btn_salvar_disponibilidade_disponibilidade.Location = new Point(851, 644);
            btn_salvar_disponibilidade_disponibilidade.Name = "btn_salvar_disponibilidade_disponibilidade";
            btn_salvar_disponibilidade_disponibilidade.Size = new Size(125, 33);
            btn_salvar_disponibilidade_disponibilidade.TabIndex = 27;
            btn_salvar_disponibilidade_disponibilidade.Text = "Salvar";
            btn_salvar_disponibilidade_disponibilidade.UseVisualStyleBackColor = false;
            btn_salvar_disponibilidade_disponibilidade.Click += btn_salvar_disponibilidade_disponibilidade_Click;
            // 
            // C_Disponibilidade
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(btn_salvar_disponibilidade_disponibilidade);
            Controls.Add(btn_editar_disponibilidade_disponibilidade);
            Controls.Add(lbl_disponibilidade);
            Controls.Add(pnl_header);
            Controls.Add(pnl_disponibilidade);
            FormBorderStyle = FormBorderStyle.None;
            Name = "C_Disponibilidade";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "C_Disponibilidade";
            pnl_disponibilidade.ResumeLayout(false);
            pnl_disponibilidade.PerformLayout();
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private TableLayoutPanel pnl_disponibilidade;
        private Label lbl_horario;
        private Label lbl_dias;
        private Label lbl_professor;
        private Panel pnl_header;
        private Button btn_voltar_disponibilidade;
        private Button btn_minimizar_disponibilidade;
        private Button btn_maximizar_disponibilidade;
        private Label lbl_titulo;
        private Button btn_fechar_disponibilidade;
        private Panel pnl_lado_C1;
        private Button btn_disciplinas;
        private Button btn_matrizes;
        private Button btn_visualizardisciplinas;
        private Button btn_disponibilidade;
        private Button btn_professores;
        private Button btn_conferirgrade;
        private Button btn_visualizarprofessores;
        private Label lbl_disponibilidade;
        private Button btn_editar_disponibilidade_disponibilidade;
        private Button btn_salvar_disponibilidade_disponibilidade;
    }
}
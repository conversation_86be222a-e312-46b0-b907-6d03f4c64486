﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using ProjetoIntegrador.Classes;
using ProjetoIntegrador.conn;

namespace ProjetoIntegrador.Coordenador.C_VisualizarDisciplinas
{
    public partial class C_VisualizarDisciplinas : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosDisciplinas;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private bool modoEdicao = false;
        private List<Label> labelsNome = new List<Label>();
        private List<Label> labelsCurso = new List<Label>();
        private List<Label> labelsAulas = new List<Label>();
        private List<Label> labelsCargaHoraria = new List<Label>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<CheckBox> checkBoxes = new List<CheckBox>();

        public C_VisualizarDisciplinas()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarDisciplinas();
        }

        private void InicializarComponentes()
        {
            // Inicializar lista de checkboxes
            checkBoxes.AddRange(new CheckBox[] {
                cb_linha1_visualizardisciplinas,
                cb_linha2_visualizardisciplinas,
                cb_linha3_visualizardisciplinas,
                cb_linha4_visualizardisciplinas,
                cb_linha5_visualizardisciplinas,
                cb_linha6_visualizardisciplinas,
                cb_linha7_visualizardisciplinas,
                cb_linha8_visualizardisciplinas,
                cb_linha9_visualizardisciplinas
            });

            // Criar labels para exibir dados
            CriarLabelsParaDados();
        }


        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Nome
                Label lblNome = new Label();
                lblNome.AutoSize = false;
                lblNome.Size = new Size(280, 40);
                lblNome.TextAlign = ContentAlignment.MiddleLeft;
                lblNome.Padding = new Padding(25, 0, 0, 0);
                lblNome.BackColor = Color.Transparent;
                lblNome.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblNome.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_DISCIPLINA");
                pnl_disciplinas.Controls.Add(lblNome, 0, i + 1);
                labelsNome.Add(lblNome);

                // Labels para Curso
                Label lblCurso = new Label();
                lblCurso.AutoSize = false;
                lblCurso.Size = new Size(190, 40);
                lblCurso.TextAlign = ContentAlignment.MiddleCenter;
                lblCurso.BackColor = Color.Transparent;
                lblCurso.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblCurso.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_CURSO");
                pnl_disciplinas.Controls.Add(lblCurso, 1, i + 1);
                labelsCurso.Add(lblCurso);

                // Labels para Aulas
                Label lblAulas = new Label();
                lblAulas.AutoSize = false;
                lblAulas.Size = new Size(190, 40);
                lblAulas.TextAlign = ContentAlignment.MiddleCenter;
                lblAulas.BackColor = Color.Transparent;
                lblAulas.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblAulas.DoubleClick += (s, e) => IniciarEdicao(s as Label, "AULAS_SEMANAIS");
                pnl_disciplinas.Controls.Add(lblAulas, 2, i + 1);
                labelsAulas.Add(lblAulas);

                // Labels para Carga Horária
                Label lblCargaHoraria = new Label();
                lblCargaHoraria.AutoSize = false;
                lblCargaHoraria.Size = new Size(190, 40);
                lblCargaHoraria.TextAlign = ContentAlignment.MiddleCenter;
                lblCargaHoraria.BackColor = Color.Transparent;
                lblCargaHoraria.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblCargaHoraria.DoubleClick += (s, e) => IniciarEdicao(s as Label, "CARGA_HORARIA");
                pnl_disciplinas.Controls.Add(lblCargaHoraria, 3, i + 1);
                labelsCargaHoraria.Add(lblCargaHoraria);
            }
        }

        private void CarregarDisciplinas()
        {
            try
            {
                dadosDisciplinas = cmdBanco.ConsultarDisciplinas();
                totalPaginas = (int)Math.Ceiling((double)dadosDisciplinas.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disciplinas: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Limpar todos os labels
            foreach (var label in labelsNome.Concat(labelsCurso).Concat(labelsAulas).Concat(labelsCargaHoraria))
            {
                label.Text = "";
                label.Tag = null;
            }

            // Ocultar todos os checkboxes
            foreach (var cb in checkBoxes)
            {
                cb.Visible = false;
                cb.Checked = false;
            }

            if (dadosDisciplinas == null || dadosDisciplinas.Rows.Count == 0)
                return;

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosDisciplinas.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;
                DataRow row = dadosDisciplinas.Rows[i];

                // Exibir checkbox
                checkBoxes[indiceLabel].Visible = true;
                checkBoxes[indiceLabel].Tag = row;

                // Preencher dados nos labels
                labelsNome[indiceLabel].Text = row["NOME_DISCIPLINA"].ToString();
                labelsNome[indiceLabel].Tag = row;

                labelsCurso[indiceLabel].Text = row["NOME_CURSO"].ToString();
                labelsCurso[indiceLabel].Tag = row;

                labelsAulas[indiceLabel].Text = row["AULAS_SEMANAIS"].ToString();
                labelsAulas[indiceLabel].Tag = row;

                labelsCargaHoraria[indiceLabel].Text = row["CARGA_HORARIA"].ToString();
                labelsCargaHoraria[indiceLabel].Tag = row;
            }

            AtualizarControlePaginacao();
        }

        private void AtualizarControlePaginacao()
        {
            lbl_paginacao.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_paginacao.Enabled = paginaAtual > 0;
            btn_proximo_paginacao.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void btn_anterior_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void btn_proximo_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void btn_professores_Click(object sender, EventArgs e)
        {

        }

        private void btn_voltar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la

        }

        private void btn_minimizar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_fechar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_salvardisciplinas_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            try
            {
                // Finalizar todas as edições pendentes
                foreach (var txtBox in textBoxesEdicao.ToList())
                {
                    FinalizarEdicao(txtBox);
                }

                // Salvar alterações no banco de dados
                foreach (DataRow row in dadosDisciplinas.Rows)
                {
                    if (row.RowState == DataRowState.Modified)
                    {
                        // Verificar se aulas semanais foi modificado
                        string aulasSemanais = row["AULAS_SEMANAIS"] != DBNull.Value ? row["AULAS_SEMANAIS"].ToString() : null;

                        cmdBanco.AtualizarDisciplina(
                            Convert.ToInt32(row["ID_DISCIPLINA"]),
                            row["NOME_DISCIPLINA"].ToString(),
                            Convert.ToInt32(row["CARGA_HORARIA"]),
                            aulasSemanais  // Passar aulas semanais para atualização
                        );
                    }
                }

                MessageBox.Show("Alterações salvas com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Recarregar dados e sair do modo edição
                modoEdicao = false;
                btn_salvardisciplinas_visualizardisponibilidades.Visible = false;
                CarregarDisciplinas();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disciplinas.GetCellPosition(label);
            pnl_disciplinas.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            // Atualizar o valor na DataRow
            if (campo == "CARGA_HORARIA")
            {
                if (int.TryParse(txtEdicao.Text, out int valor))
                {
                    row[campo] = valor;
                    label.Text = txtEdicao.Text;
                }
                else
                {
                    MessageBox.Show("Carga horária deve ser um número inteiro.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }
            else if (campo == "AULAS_SEMANAIS")
            {
                // Validar aulas semanais (deve ser 1, 2, 3, 4 ou 5)
                if (int.TryParse(txtEdicao.Text, out int aulasValor) && aulasValor >= 1 && aulasValor <= 5)
                {
                    row[campo] = txtEdicao.Text;
                    label.Text = txtEdicao.Text;
                }
                else
                {
                    MessageBox.Show("Aulas semanais deve ser um número entre 1 e 5.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }
            else
            {
                row[campo] = txtEdicao.Text;
                label.Text = txtEdicao.Text;
            }

            // Remover TextBox e mostrar label
            pnl_disciplinas.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            // Remover TextBox e mostrar label sem alterar dados
            pnl_disciplinas.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void btn_editardisciplinas_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvardisciplinas_visualizardisponibilidades.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btn_excluir_disciplinas_Click(object sender, EventArgs e)
        {
            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos uma disciplina para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter nomes das disciplinas selecionadas para confirmação
            var nomesDisciplinas = checkboxesSelecionados
                .Select(cb => ((DataRow)cb.Tag)["NOME_DISCIPLINA"].ToString())
                .ToList();

            string mensagemConfirmacao;
            if (nomesDisciplinas.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir a disciplina '{nomesDisciplinas[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {nomesDisciplinas.Count} disciplinas selecionadas?\n\n" +
                                    string.Join("\n", nomesDisciplinas.Take(5)) +
                                    (nomesDisciplinas.Count > 5 ? "\n..." : "");
            }

            // Confirmar exclusão
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    int disciplinasExcluidas = 0;
                    var erros = new List<string>();

                    foreach (var checkbox in checkboxesSelecionados)
                    {
                        DataRow row = (DataRow)checkbox.Tag;
                        int idDisciplina = Convert.ToInt32(row["ID_DISCIPLINA"]);
                        string nomeDisciplina = row["NOME_DISCIPLINA"].ToString();

                        try
                        {
                            cmdBanco.DeletarDisciplina(idDisciplina);
                            disciplinasExcluidas++;
                        }
                        catch (Exception ex)
                        {
                            erros.Add($"Erro ao excluir '{nomeDisciplina}': {ex.Message}");
                        }
                    }

                    // Exibir resultado
                    if (erros.Count == 0)
                    {
                        string mensagemSucesso = disciplinasExcluidas == 1
                            ? "Disciplina excluída com sucesso!"
                            : $"{disciplinasExcluidas} disciplinas excluídas com sucesso!";

                        MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagemErro = $"{disciplinasExcluidas} disciplinas excluídas com sucesso.\n\n" +
                                            $"Erros encontrados:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarDisciplinas();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro inesperado ao excluir disciplinas: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void pnl_disciplinas_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
